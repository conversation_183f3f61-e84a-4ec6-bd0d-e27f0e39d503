---
type: "always_apply"
---

## 规则1：语言使用规范
- 必须使用中文进行所有回复和交流
- 所有解释、说明、文档均使用中文表达
- 确保语言表达准确、清晰

## 规则2：代码分析规范
- 必须基于完整的代码理解进行分析和修改
- 确保对代码功能有全面、准确的理解

## 规则3：代码修改验证规范
- 每次代码修改完成后必须立即执行编译验证
- 如果编译出现错误，必须继续修复直至所有错误解决
- 不允许留下任何编译错误或警告
- 确保修改后的代码能够成功编译运行

## 规则4：功能完整性保护规范
- 修复代码错误时禁止删除或简化原有功能
- 不允许通过移除代码来"解决"编译错误
- 必须在保持原有功能完整性的前提下进行错误修复
- 确保修复后的代码功能不少于修复前

## 规则5：信息获取准确性规范
- 遇到不确定的内容或代码结构时，必须使用工具查看相关文件
- 禁止基于猜测、假设或经验进行回答
- 禁止编造或虚构不存在的信息
- 所有回答和修改必须基于实际的文件内容和代码结构

## 规则6：文档生成限制规范
- 禁止在任务完成后主动生成总结性markdown文件
- 仅在用户明确要求时才创建文档文件
- 避免产生不必要的文件，保持工作空间整洁
- 专注于完成用户的具体技术需求

## 规则7：Enhance prompt功能的语言要求
-当使用Enhance prompt功能时，增强之后的提示词，必须用中文显示

## 规则8：程序自动启动的限制
-每次执行完成任务后，不要自动启动程序

## 规则9：测试程序文件的规则
- 不允许自行添加测试程序，除非人为要求
- 创建的测试程序文件，必须放置在根目录文件下的“Tests”文件夹下

## 规则10：文本要求
- 创建文本和回答时，必须使用通俗易懂的直白语言
- 不要生成不必要的多余内容，只针对必要的内容进行文本生成