using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using PC_Control2.Demo.Models;
using PC_Control2.Demo.ViewModels;

namespace PC_Control2.Demo.Controls
{
    /// <summary>
    /// SFCStepView.xaml 的交互逻辑
    /// </summary>
    public partial class SFCStepView : UserControl
    {
        #region 私有字段

        private SFCConnectPoint? _topConnectPoint;
        private SFCConnectPoint? _bottomConnectPoint;
        private SFCStepViewModel? _viewModel;

        // 拖拽相关
        private bool _isDragging = false;
        private Point _dragStartPoint;

        #endregion

        #region 属性

        /// <summary>
        /// ViewModel属性
        /// </summary>
        public SFCStepViewModel? ViewModel => _viewModel;

        #endregion

        #region 依赖属性
        
        /// <summary>
        /// 连接交互管理器依赖属性
        /// </summary>
        public static readonly DependencyProperty InteractionManagerProperty =
            DependencyProperty.Register("InteractionManager", typeof(SFCConnectPointInteractionManager), typeof(SFCStepView),
                new PropertyMetadata(null, OnInteractionManagerChanged));
                
        #endregion
        
        #region 公共属性
        
        /// <summary>
        /// 连接交互管理器
        /// </summary>
        public SFCConnectPointInteractionManager? InteractionManager
        {
            get { return (SFCConnectPointInteractionManager?)GetValue(InteractionManagerProperty); }
            set { SetValue(InteractionManagerProperty, value); }
        }
        
        #endregion
        
        #region 构造函数
        
        public SFCStepView()
        {
            InitializeComponent();

            // 数据上下文变化事件
            DataContextChanged += OnDataContextChanged;

            // 控件加载完成事件
            Loaded += OnLoaded;

            // 设置可拖拽
            this.MouseLeftButtonDown += OnMouseLeftButtonDown;
            this.MouseLeftButtonUp += OnMouseLeftButtonUp;
            this.MouseMove += OnMouseMove;
        }
        
        #endregion
        
        #region 事件处理
        
        /// <summary>
        /// 数据上下文变化处理
        /// </summary>
        private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            _viewModel = e.NewValue as SFCStepViewModel;
            
            // 初始化连接点
            if (_viewModel != null)
            {
                InitializeConnectPoints();
            }
        }
        
        /// <summary>
        /// 控件加载完成处理
        /// </summary>
        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            // 初始化连接点
            InitializeConnectPoints();
        }
        
        /// <summary>
        /// 交互管理器变化处理
        /// </summary>
        private static void OnInteractionManagerChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is SFCStepView stepView)
            {
                stepView.UpdateConnectPointsInteractionManager();
            }
        }
        
        #endregion
        
        #region 私有方法
        
        /// <summary>
        /// 初始化连接点
        /// </summary>
        private void InitializeConnectPoints()
        {
            if (_viewModel == null)
                return;
                
            // 查找连接点控件
            _topConnectPoint = TopConnectPoint as SFCConnectPoint;
            _bottomConnectPoint = BottomConnectPoint as SFCConnectPoint;
            
            // 如果连接点控件不存在，则创建
            if (_topConnectPoint == null)
            {
                _topConnectPoint = new SFCConnectPoint
                {
                    ElementId = _viewModel.Id,
                    ElementType = SFCElementType.Step,
                    PointType = ConnectPointType.Input,
                    Index = 0
                };
                
                // 替换原有连接点
                if (TopConnectPoint != null)
                {
                    int index = MainCanvas.Children.IndexOf(TopConnectPoint);
                    if (index >= 0)
                    {
                        MainCanvas.Children[index] = _topConnectPoint;
                        Canvas.SetLeft(_topConnectPoint, Canvas.GetLeft(TopConnectPoint));
                        Canvas.SetTop(_topConnectPoint, Canvas.GetTop(TopConnectPoint));
                    }
                }
            }
            
            if (_bottomConnectPoint == null)
            {
                _bottomConnectPoint = new SFCConnectPoint
                {
                    ElementId = _viewModel.Id,
                    ElementType = SFCElementType.Step,
                    PointType = ConnectPointType.Output,
                    Index = 0
                };
                
                // 替换原有连接点
                if (BottomConnectPoint != null)
                {
                    int index = MainCanvas.Children.IndexOf(BottomConnectPoint);
                    if (index >= 0)
                    {
                        MainCanvas.Children[index] = _bottomConnectPoint;
                        Canvas.SetLeft(_bottomConnectPoint, Canvas.GetLeft(BottomConnectPoint));
                        Canvas.SetTop(_bottomConnectPoint, Canvas.GetTop(BottomConnectPoint));
                    }
                }
            }
            
            // 设置连接点适配器
            if (_viewModel.ConnectPointAdapters != null && _viewModel.ConnectPointAdapters.Count >= 2)
            {
                if (_topConnectPoint != null)
                {
                    _topConnectPoint.Adapter = _viewModel.ConnectPointAdapters[0];
                }
                
                if (_bottomConnectPoint != null)
                {
                    _bottomConnectPoint.Adapter = _viewModel.ConnectPointAdapters[1];
                }
            }
            
            // 更新交互管理器
            UpdateConnectPointsInteractionManager();
        }
        
        /// <summary>
        /// 更新连接点的交互管理器
        /// </summary>
        private void UpdateConnectPointsInteractionManager()
        {
            if (_topConnectPoint != null)
            {
                _topConnectPoint.InteractionManager = InteractionManager;
            }
            
            if (_bottomConnectPoint != null)
            {
                _bottomConnectPoint.InteractionManager = InteractionManager;
            }
        }

        #endregion

        #region 拖拽功能

        private void OnMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (ViewModel == null) return;

            // 选中步骤
            ViewModel.SelectCommand?.Execute(null);

            // 开始拖拽
            _isDragging = true;
            _dragStartPoint = e.GetPosition(this);
            this.CaptureMouse();

            // 不设置e.Handled = true，让事件继续冒泡到SFCCanvas
        }

        private void OnMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isDragging)
            {
                _isDragging = false;
                this.ReleaseMouseCapture();
                e.Handled = true;
            }
        }

        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            if (_isDragging && ViewModel != null && e.LeftButton == MouseButtonState.Pressed)
            {
                var currentPosition = e.GetPosition(this);
                var deltaX = currentPosition.X - _dragStartPoint.X;
                var deltaY = currentPosition.Y - _dragStartPoint.Y;

                // 更新步骤位置
                var newPosition = new Point(
                    ViewModel.Position.X + deltaX,
                    ViewModel.Position.Y + deltaY);

                System.Diagnostics.Debug.WriteLine($"[SFCStepView] 拖动更新位置: {ViewModel.Id} -> {newPosition}");
                ViewModel.Position = newPosition;
                e.Handled = true;
            }
        }

        #endregion
    }
}
