## 关于SFC顺控器元素的连接线

我要更新一下设计意图：
1.当选中现有的元素插入另一个新的元素时；默认把新元素的输入连接点和现有元素的输出连接点进行重叠；
2.但是即使重叠了，后台应该也是有连接线连接的，只是由于两个连接点重叠，被盖住了，所以看不到连接线；如果我把元素移开，应该能看到两个连接点之间的连接线。
3.连接线，也请使用和蓝图编辑器中，一样的贝塞尔曲线这种方式；
4.若两个连接点之间已经完全没有重叠的部分了，则需要显示连接线；并且连接线的两端一定是落在对应的连接点上，不能悬空；
5.若两个连接点有重叠部分，只要有一点重叠，则隐藏连接线；

## 预期效果

测试步骤
1. 基本连接测试
启动程序，打开SFC编辑器
添加步骤S1：在画布上点击添加一个步骤
选中S1，然后插入转换条件T1
选中T1，然后插入步骤S2
2. 连接线显示测试
观察以下几个关键点：

连接点位置对齐：

S1的下连接点（绿色圆点）和T1的上连接点（白色圆点）应该完全重叠
T1的下连接点（绿色圆点）和S2的上连接点（白色圆点）应该完全重叠
连接线终点连接：

从S1下连接点出发的贝塞尔曲线应该精确连接到T1的上连接点，不再悬空；此时因为两个连接点完全重叠，所以看不到连接线；
从T1下连接点出发的贝塞尔曲线应该精确连接到S2的上连接点，不再悬空；此时因为两个连接点完全重叠，所以看不到连接线；
3. 元素移动测试
拖动T1元素，将其稍微移开S1和S2
观察连接线：
应该能看到从S1到T1的完整贝塞尔曲线连接线
应该能看到从T1到S2的完整贝塞尔曲线连接线
连接线的起点和终点都应该准确连接到对应的连接点上
4. 连接点重叠测试
将T1拖回原位置，使连接点重新重叠
观察连接线：当连接点距离很近时（小于10像素），连接线应该自动隐藏，显示连接点重叠效果
测试成功的标志：

✅ 连接线不再悬空，精确连接到目标连接点
✅ 连接点位置完全重叠（绿色+白色圆点重合）
✅ 移动元素后连接线正确显示和更新
✅ 连接点重叠时连接线自动隐藏