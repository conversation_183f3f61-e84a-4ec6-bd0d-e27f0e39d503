using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using PC_Control2.Demo.Models;
using PC_Control2.Demo.ViewModels;

namespace PC_Control2.Demo.Controls
{
    /// <summary>
    /// SFCParallelBranchView.xaml 的交互逻辑
    /// </summary>
    public partial class SFCParallelBranchView : UserControl
    {
        #region 私有字段

        private SFCConnectPoint? _leftTopConnectPoint;
        private SFCConnectPoint? _leftParallelPoint;
        private SFCConnectPoint? _rightParallelPoint;
        private SFCBranchViewModel? _viewModel;

        // 拖拽相关
        private bool _isDragging = false;
        private Point _dragStartPoint;

        #endregion

        #region 属性

        /// <summary>
        /// ViewModel属性
        /// </summary>
        public SFCBranchViewModel? ViewModel => _viewModel;

        #endregion

        #region 依赖属性
        
        /// <summary>
        /// 连接交互管理器依赖属性
        /// </summary>
        public static readonly DependencyProperty InteractionManagerProperty =
            DependencyProperty.Register("InteractionManager", typeof(SFCConnectPointInteractionManager), typeof(SFCParallelBranchView),
                new PropertyMetadata(null, OnInteractionManagerChanged));
                
        #endregion
        
        #region 公共属性
        
        /// <summary>
        /// 连接交互管理器
        /// </summary>
        public SFCConnectPointInteractionManager? InteractionManager
        {
            get { return (SFCConnectPointInteractionManager?)GetValue(InteractionManagerProperty); }
            set { SetValue(InteractionManagerProperty, value); }
        }
        
        #endregion
        
        #region 构造函数
        
        public SFCParallelBranchView()
        {
            InitializeComponent();

            // 数据上下文变化事件
            DataContextChanged += OnDataContextChanged;

            // 控件加载完成事件
            Loaded += OnLoaded;

            // 设置可拖拽
            this.MouseLeftButtonDown += OnMouseLeftButtonDown;
            this.MouseLeftButtonUp += OnMouseLeftButtonUp;
            this.MouseMove += OnMouseMove;
        }
        
        #endregion
        
        #region 事件处理
        
        /// <summary>
        /// 数据上下文变化处理
        /// </summary>
        private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            _viewModel = e.NewValue as SFCBranchViewModel;
            
            // 初始化连接点
            if (_viewModel != null)
            {
                InitializeConnectPoints();
            }
        }
        
        /// <summary>
        /// 控件加载完成处理
        /// </summary>
        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            // 初始化连接点
            InitializeConnectPoints();
        }
        
        /// <summary>
        /// 交互管理器变化处理
        /// </summary>
        private static void OnInteractionManagerChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is SFCParallelBranchView branchView)
            {
                branchView.UpdateConnectPointsInteractionManager();
            }
        }
        
        #endregion
        
        #region 私有方法
        
        /// <summary>
        /// 初始化连接点
        /// </summary>
        private void InitializeConnectPoints()
        {
            if (_viewModel == null)
                return;
                
            // 查找连接点控件
            _leftTopConnectPoint = LeftTopConnectPoint as SFCConnectPoint;
            _leftParallelPoint = LeftParallelPoint as SFCConnectPoint;
            _rightParallelPoint = RightParallelPoint as SFCConnectPoint;
            
            // 如果连接点控件不存在，则创建
            if (_leftTopConnectPoint == null)
            {
                _leftTopConnectPoint = new SFCConnectPoint
                {
                    ElementId = _viewModel.Id,
                    ElementType = SFCElementType.ParallelBranch,
                    PointType = ConnectPointType.Input,
                    Index = 0
                };
                
                // 替换原有连接点
                if (LeftTopConnectPoint != null)
                {
                    int index = MainCanvas.Children.IndexOf(LeftTopConnectPoint);
                    if (index >= 0)
                    {
                        MainCanvas.Children[index] = _leftTopConnectPoint;
                        Canvas.SetLeft(_leftTopConnectPoint, Canvas.GetLeft(LeftTopConnectPoint));
                        Canvas.SetTop(_leftTopConnectPoint, Canvas.GetTop(LeftTopConnectPoint));
                    }
                }
            }
            
            if (_leftParallelPoint == null)
            {
                _leftParallelPoint = new SFCConnectPoint
                {
                    ElementId = _viewModel.Id,
                    ElementType = SFCElementType.ParallelBranch,
                    PointType = ConnectPointType.Input,
                    Index = 1
                };
                
                // 替换原有连接点
                if (LeftParallelPoint != null)
                {
                    int index = MainCanvas.Children.IndexOf(LeftParallelPoint);
                    if (index >= 0)
                    {
                        MainCanvas.Children[index] = _leftParallelPoint;
                        Canvas.SetLeft(_leftParallelPoint, Canvas.GetLeft(LeftParallelPoint));
                        Canvas.SetTop(_leftParallelPoint, Canvas.GetTop(LeftParallelPoint));
                    }
                }
            }
            
            if (_rightParallelPoint == null)
            {
                _rightParallelPoint = new SFCConnectPoint
                {
                    ElementId = _viewModel.Id,
                    ElementType = SFCElementType.ParallelBranch,
                    PointType = ConnectPointType.Output,
                    Index = 0
                };
                
                // 替换原有连接点
                if (RightParallelPoint != null)
                {
                    int index = MainCanvas.Children.IndexOf(RightParallelPoint);
                    if (index >= 0)
                    {
                        MainCanvas.Children[index] = _rightParallelPoint;
                        Canvas.SetLeft(_rightParallelPoint, Canvas.GetLeft(RightParallelPoint));
                        Canvas.SetTop(_rightParallelPoint, Canvas.GetTop(RightParallelPoint));
                    }
                }
            }
            
            // 设置连接点适配器
            if (_viewModel.ConnectPointAdapters != null && _viewModel.ConnectPointAdapters.Count >= 4)
            {
                if (_leftTopConnectPoint != null)
                {
                    _leftTopConnectPoint.Adapter = _viewModel.ConnectPointAdapters[0];
                }
                
                if (_leftParallelPoint != null)
                {
                    _leftParallelPoint.Adapter = _viewModel.ConnectPointAdapters[1];
                }
                
                if (_rightParallelPoint != null)
                {
                    _rightParallelPoint.Adapter = _viewModel.ConnectPointAdapters[2];
                }
            }
            
            // 更新交互管理器
            UpdateConnectPointsInteractionManager();
        }
        
        /// <summary>
        /// 更新连接点的交互管理器
        /// </summary>
        private void UpdateConnectPointsInteractionManager()
        {
            if (_leftTopConnectPoint != null)
            {
                _leftTopConnectPoint.InteractionManager = InteractionManager;
            }
            
            if (_leftParallelPoint != null)
            {
                _leftParallelPoint.InteractionManager = InteractionManager;
            }
            
            if (_rightParallelPoint != null)
            {
                _rightParallelPoint.InteractionManager = InteractionManager;
            }
        }

        #endregion

        #region 拖拽功能

        private void OnMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (ViewModel == null) return;

            // 选中并行分支
            ViewModel.SelectCommand?.Execute(null);

            // 开始拖拽
            _isDragging = true;
            _dragStartPoint = e.GetPosition(this);
            this.CaptureMouse();

            // 不设置e.Handled = true，让事件继续冒泡到SFCCanvas
        }

        private void OnMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isDragging)
            {
                _isDragging = false;
                this.ReleaseMouseCapture();
                e.Handled = true;
            }
        }

        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            if (_isDragging && ViewModel != null && e.LeftButton == MouseButtonState.Pressed)
            {
                var currentPosition = e.GetPosition(this);
                var deltaX = currentPosition.X - _dragStartPoint.X;
                var deltaY = currentPosition.Y - _dragStartPoint.Y;

                // 更新并行分支位置
                var newPosition = new Point(
                    ViewModel.Position.X + deltaX,
                    ViewModel.Position.Y + deltaY);

                System.Diagnostics.Debug.WriteLine($"[SFCParallelBranchView] 拖动更新位置: {ViewModel.Id} -> {newPosition}");
                ViewModel.Position = newPosition;
                e.Handled = true;
            }
        }

        #endregion
    }
}
